<!-- Generated by SpecStory -->

# Stata观察数据的指令 (2025-05-19 22:30:06)

_**User**_

stata中观察数据的指令有那些

---

_**Assistant**_



---

Stata中观察数据的指令主要有：

1. `list` - 列出数据集内容
2. `browse` - 以电子表格形式查看数据
3. `describe` - 显示变量特性和数据结构
4. `summarize` - 提供数据的描述性统计
5. `tabulate` - 生成频率表或交叉表
6. `codebook` - 查看变量的详细信息
7. `inspect` - 检查变量的分布情况
8. `table` - 创建简单或复杂的表格
9. `misstable` - 分析缺失值模式
10. `count` - 计数满足条件的观测值
11. `display` - 显示表达式的结果

图形化查看数据的指令：
1. `graph` - 创建各类图形
2. `histogram` - 绘制直方图
3. `scatter` - 绘制散点图
4. `box` - 绘制箱线图

---

